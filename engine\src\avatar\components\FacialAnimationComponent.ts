/**
 * 面部动画组件
 * 用于控制角色的面部表情和口型同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';

/**
 * 面部表情类型
 */
export enum FacialExpressionType {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  FEAR = 'fear',
  FEARFUL = 'fearful',
  DISGUST = 'disgust',
  DISGUSTED = 'disgusted',
  CONTEMPT = 'contempt'
}

/**
 * 口型类型
 */
export enum VisemeType {
  NEUTRAL = 'neutral',
  SILENT = 'silent',
  AA = 'aa',
  E = 'e',
  EE = 'ee',
  IH = 'ih',
  OH = 'oh',
  OU = 'ou',
  PP = 'pp',
  FF = 'ff',
  TH = 'th',
  DD = 'dd',
  K = 'k',
  KK = 'kk',
  CH = 'ch',
  SS = 'ss',
  NN = 'nn',
  R = 'r',
  RR = 'rr',
  MM = 'mm'
}

/**
 * 面部表情数据
 */
export interface FacialExpression {
  expression: FacialExpressionType;
  weight: number;
}

/**
 * 口型数据
 */
export interface Viseme {
  viseme: VisemeType;
  weight: number;
}

/**
 * 面部动画组件类型
 */
export const FacialAnimationComponentType = 'FacialAnimationComponent';

/**
 * 面部动画组件
 */
export class FacialAnimationComponent extends Component {
  /** 组件类型 */
  static readonly TYPE = FacialAnimationComponentType;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 当前表情 */
  private currentExpression: FacialExpression = { expression: FacialExpressionType.NEUTRAL, weight: 1.0 };

  /** 当前口型 */
  private currentViseme: Viseme = { viseme: VisemeType.NEUTRAL, weight: 0.0 };

  /** 表情混合映射 */
  private expressionBlendMap: Map<FacialExpressionType, number> = new Map();

  /** 口型混合映射 */
  private visemeBlendMap: Map<VisemeType, number> = new Map();

  /** 表情混合速度 */
  private expressionBlendSpeed: number = 5.0;

  /** 口型混合速度 */
  private visemeBlendSpeed: number = 10.0;

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(FacialAnimationComponent.TYPE);
    this.setEntity(entity);

    // 初始化表情混合映射
    for (const expression of Object.values(FacialExpressionType)) {
      this.expressionBlendMap.set(expression, 0);
    }
    this.expressionBlendMap.set(FacialExpressionType.NEUTRAL, 1.0);

    // 初始化口型混合映射
    for (const viseme of Object.values(VisemeType)) {
      this.visemeBlendMap.set(viseme, 0);
    }
    this.visemeBlendMap.set(VisemeType.NEUTRAL, 1.0);
  }

  /**
   * 获取组件类型
   */
  public getType(): string {
    return FacialAnimationComponent.TYPE;
  }

  /**
   * 设置表情
   * @param expression 表情类型
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   */
  public setExpression(expression: FacialExpressionType, weight: number = 1.0, blendTime?: number): void {
    if (!this.isEnabled()) return;

    this.currentExpression = { expression, weight };

    if (blendTime !== undefined) {
      this.expressionBlendSpeed = 1.0 / Math.max(0.001, blendTime);
    }

    // 更新混合映射
    for (const [key, _] of this.expressionBlendMap.entries()) {
      if (key === expression) {
        this.expressionBlendMap.set(key, weight);
      } else {
        this.expressionBlendMap.set(key, 0);
      }
    }

    this.eventEmitter.emit('expressionChange', { expression, weight });
  }

  /**
   * 重置表情
   */
  public resetExpression(): void {
    this.setExpression(FacialExpressionType.NEUTRAL);
  }

  /**
   * 获取当前表情
   */
  public getCurrentExpression(): FacialExpression {
    return this.currentExpression;
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   */
  public setViseme(viseme: VisemeType, weight: number = 1.0, blendTime?: number): void {
    if (!this.isEnabled()) return;

    this.currentViseme = { viseme, weight };

    if (blendTime !== undefined) {
      this.visemeBlendSpeed = 1.0 / Math.max(0.001, blendTime);
    }

    // 更新混合映射
    for (const [key, _] of this.visemeBlendMap.entries()) {
      if (key === viseme) {
        this.visemeBlendMap.set(key, weight);
      } else {
        this.visemeBlendMap.set(key, 0);
      }
    }

    this.eventEmitter.emit('visemeChange', { viseme, weight });
  }

  /**
   * 重置口型
   */
  public resetViseme(): void {
    this.setViseme(VisemeType.NEUTRAL);
  }

  /**
   * 获取当前口型
   */
  public getCurrentViseme(): Viseme {
    return this.currentViseme;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) return;

    // 使用 deltaTime 和混合速度进行平滑过渡
    this.updateBlending(deltaTime);
  }

  /**
   * 更新混合
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateBlending(deltaTime: number): void {
    // 使用 expressionBlendSpeed 和 visemeBlendSpeed 进行平滑过渡
    const expressionBlendFactor = Math.min(1.0, this.expressionBlendSpeed * deltaTime);
    const visemeBlendFactor = Math.min(1.0, this.visemeBlendSpeed * deltaTime);

    // 这里可以添加更复杂的混合逻辑
    if (expressionBlendFactor > 0 || visemeBlendFactor > 0) {
      // 混合逻辑已在 setExpression 和 setViseme 方法中处理
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.off(event, callback);
  }
}
