/**
 * 中文口型同步系统
 * 专门针对中文语音的口型同步
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent, VisemeType } from '../components/FacialAnimationComponent';
import { LipSyncComponent } from '../components/LipSyncComponent';
import { LipSyncSystem, LipSyncSystemConfig } from './LipSyncSystem';

/**
 * 中文音素类型
 */
export enum ChinesePhonemeType {
  // 声母
  B = 'b',   // 不送气浊音，如"爸"
  P = 'p',   // 送气清音，如"怕"
  M = 'm',   // 鼻音，如"妈"
  F = 'f',   // 唇齿音，如"发"
  D = 'd',   // 不送气浊音，如"大"
  T = 't',   // 送气清音，如"他"
  N = 'n',   // 鼻音，如"那"
  L = 'l',   // 边音，如"拉"
  G = 'g',   // 不送气浊音，如"哥"
  K = 'k',   // 送气清音，如"科"
  H = 'h',   // 喉音，如"河"
  J = 'j',   // 不送气浊音，如"鸡"
  Q = 'q',   // 送气清音，如"七"
  X = 'x',   // 擦音，如"西"
  ZH = 'zh', // 不送气浊音，如"知"
  CH = 'ch', // 送气清音，如"吃"
  SH = 'sh', // 擦音，如"是"
  R = 'r',   // 擦音，如"日"
  Z = 'z',   // 不送气浊音，如"字"
  C = 'c',   // 送气清音，如"此"
  S = 's',   // 擦音，如"思"

  // 韵母
  A = 'a',     // 如"啊"
  O = 'o',     // 如"哦"
  E = 'e',     // 如"鹅"
  I = 'i',     // 如"衣"
  U = 'u',     // 如"乌"
  V = 'v',     // 如"鱼"
  AI = 'ai',   // 如"爱"
  EI = 'ei',   // 如"诶"
  UI = 'ui',   // 如"位"
  AO = 'ao',   // 如"奥"
  OU = 'ou',   // 如"欧"
  IU = 'iu',   // 如"有"
  IE = 'ie',   // 如"也"
  VE = 've',   // 如"月"
  ER = 'er',   // 如"儿"
  AN = 'an',   // 如"安"
  EN = 'en',   // 如"恩"
  IN = 'in',   // 如"因"
  UN = 'un',   // 如"温"
  VN = 'vn',   // 如"晕"
  ANG = 'ang', // 如"昂"
  ENG = 'eng', // 如"能"
  ING = 'ing', // 如"英"
  ONG = 'ong', // 如"翁"

  // 特殊
  SILENT = 'silent' // 静音
}

/**
 * 中文口型同步系统配置
 */
export interface ChineseLipSyncSystemConfig extends LipSyncSystemConfig {
  /** 是否使用拼音分析 */
  usePinyinAnalysis?: boolean;
  /** 是否使用声调分析 */
  useToneAnalysis?: boolean;
  /** 是否使用语音识别 */
  useSpeechRecognition?: boolean;
  /** 语音识别语言 */
  speechRecognitionLang?: string;
  /** 是否使用音素分析 */
  usePhonemeAnalysis?: boolean;
  /** 是否使用平滑过渡 */
  useSmoothTransition?: boolean;
  /** 过渡时间（秒） */
  transitionTime?: number;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
  /** 是否使用上下文预测 */
  useContextPrediction?: boolean;
}

/**
 * 中文口型同步系统
 */
export class ChineseLipSyncSystem extends LipSyncSystem {
  /** 系统名称 */
  static readonly NAME = 'ChineseLipSyncSystem';

  /** 配置 */
  protected config: ChineseLipSyncSystemConfig;

  /** 拼音到口型映射 */
  private pinyinToVisemeMap: Map<string, VisemeType> = new Map();

  /** 音素到口型映射 */
  private phonemeToVisemeMap: Map<ChinesePhonemeType, VisemeType> = new Map();

  /** 当前音素序列 */
  private currentPhonemes: ChinesePhonemeType[] = [];

  /** 当前音素索引 */
  private currentPhonemeIndex: number = 0;

  /** 语音识别器 */
  private speechRecognition: any = null;

  /** 当前口型 */
  private currentViseme: VisemeType = VisemeType.SILENT;

  /** 上一个口型 */
  private previousViseme: VisemeType = VisemeType.SILENT;

  /** 口型历史 */
  private visemeHistory: VisemeType[] = [];

  /** 口型转换矩阵 */
  private visemeTransitionMatrix: Map<VisemeType, Map<VisemeType, number>> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: ChineseLipSyncSystemConfig = {}) {
    super(config);

    this.config = {
      ...config,
      usePinyinAnalysis: config.usePinyinAnalysis !== undefined ? config.usePinyinAnalysis : true,
      useToneAnalysis: config.useToneAnalysis !== undefined ? config.useToneAnalysis : false,
      useSpeechRecognition: config.useSpeechRecognition !== undefined ? config.useSpeechRecognition : true,
      speechRecognitionLang: config.speechRecognitionLang || 'zh-CN',
      usePhonemeAnalysis: config.usePhonemeAnalysis !== undefined ? config.usePhonemeAnalysis : true,
      useSmoothTransition: config.useSmoothTransition !== undefined ? config.useSmoothTransition : true,
      transitionTime: config.transitionTime || 0.1,
      contextWindowSize: config.contextWindowSize || 5,
      useContextPrediction: config.useContextPrediction !== undefined ? config.useContextPrediction : true
    };

    // 初始化拼音到口型映射
    this.initPinyinToVisemeMap();

    // 初始化音素到口型映射
    this.initPhonemeToVisemeMap();

    // 初始化口型转换矩阵
    this.initVisemeTransitionMatrix();

    // 初始化语音识别
    if (this.config.useSpeechRecognition) {
      this.initSpeechRecognition();
    }
  }

  /**
   * 初始化拼音到口型映射
   */
  private initPinyinToVisemeMap(): void {
    // 声母映射
    this.pinyinToVisemeMap.set('b', VisemeType.MM);
    this.pinyinToVisemeMap.set('p', VisemeType.MM);
    this.pinyinToVisemeMap.set('m', VisemeType.MM);
    this.pinyinToVisemeMap.set('f', VisemeType.FF);
    this.pinyinToVisemeMap.set('d', VisemeType.DD);
    this.pinyinToVisemeMap.set('t', VisemeType.DD);
    this.pinyinToVisemeMap.set('n', VisemeType.NN);
    this.pinyinToVisemeMap.set('l', VisemeType.NN);
    this.pinyinToVisemeMap.set('g', VisemeType.KK);
    this.pinyinToVisemeMap.set('k', VisemeType.KK);
    this.pinyinToVisemeMap.set('h', VisemeType.KK);
    this.pinyinToVisemeMap.set('j', VisemeType.CH);
    this.pinyinToVisemeMap.set('q', VisemeType.CH);
    this.pinyinToVisemeMap.set('x', VisemeType.SS);
    this.pinyinToVisemeMap.set('zh', VisemeType.CH);
    this.pinyinToVisemeMap.set('ch', VisemeType.CH);
    this.pinyinToVisemeMap.set('sh', VisemeType.SS);
    this.pinyinToVisemeMap.set('r', VisemeType.RR);
    this.pinyinToVisemeMap.set('z', VisemeType.DD);
    this.pinyinToVisemeMap.set('c', VisemeType.DD);
    this.pinyinToVisemeMap.set('s', VisemeType.SS);

    // 韵母映射
    this.pinyinToVisemeMap.set('a', VisemeType.AA);
    this.pinyinToVisemeMap.set('o', VisemeType.OU);
    this.pinyinToVisemeMap.set('e', VisemeType.EH);
    this.pinyinToVisemeMap.set('i', VisemeType.EE);
    this.pinyinToVisemeMap.set('u', VisemeType.OU);
    this.pinyinToVisemeMap.set('v', VisemeType.EE);
    this.pinyinToVisemeMap.set('ai', VisemeType.AA);
    this.pinyinToVisemeMap.set('ei', VisemeType.EH);
    this.pinyinToVisemeMap.set('ui', VisemeType.EE);
    this.pinyinToVisemeMap.set('ao', VisemeType.AA);
    this.pinyinToVisemeMap.set('ou', VisemeType.OU);
    this.pinyinToVisemeMap.set('iu', VisemeType.OU);
    this.pinyinToVisemeMap.set('ie', VisemeType.EH);
    this.pinyinToVisemeMap.set('ve', VisemeType.EH);
    this.pinyinToVisemeMap.set('er', VisemeType.ER);
    this.pinyinToVisemeMap.set('an', VisemeType.AA);
    this.pinyinToVisemeMap.set('en', VisemeType.EH);
    this.pinyinToVisemeMap.set('in', VisemeType.EE);
    this.pinyinToVisemeMap.set('un', VisemeType.OU);
    this.pinyinToVisemeMap.set('vn', VisemeType.EE);
    this.pinyinToVisemeMap.set('ang', VisemeType.AA);
    this.pinyinToVisemeMap.set('eng', VisemeType.EH);
    this.pinyinToVisemeMap.set('ing', VisemeType.EE);
    this.pinyinToVisemeMap.set('ong', VisemeType.OU);
  }

  /**
   * 初始化音素到口型映射
   */
  private initPhonemeToVisemeMap(): void {
    // 声母映射
    this.phonemeToVisemeMap.set(ChinesePhonemeType.B, VisemeType.MM);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.P, VisemeType.MM);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.M, VisemeType.MM);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.F, VisemeType.FF);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.D, VisemeType.DD);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.T, VisemeType.DD);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.N, VisemeType.NN);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.L, VisemeType.NN);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.G, VisemeType.KK);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.K, VisemeType.KK);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.H, VisemeType.KK);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.J, VisemeType.CH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.Q, VisemeType.CH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.X, VisemeType.SS);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.ZH, VisemeType.CH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.CH, VisemeType.CH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.SH, VisemeType.SS);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.R, VisemeType.RR);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.Z, VisemeType.DD);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.C, VisemeType.DD);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.S, VisemeType.SS);

    // 韵母映射
    this.phonemeToVisemeMap.set(ChinesePhonemeType.A, VisemeType.AA);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.O, VisemeType.OU);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.E, VisemeType.EH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.I, VisemeType.EE);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.U, VisemeType.OU);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.V, VisemeType.EE);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.AI, VisemeType.AA);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.EI, VisemeType.EH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.UI, VisemeType.EE);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.AO, VisemeType.AA);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.OU, VisemeType.OU);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.IU, VisemeType.OU);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.IE, VisemeType.EH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.VE, VisemeType.EH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.ER, VisemeType.ER);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.AN, VisemeType.AA);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.EN, VisemeType.EH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.IN, VisemeType.EE);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.UN, VisemeType.OU);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.VN, VisemeType.EE);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.ANG, VisemeType.AA);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.ENG, VisemeType.EH);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.ING, VisemeType.EE);
    this.phonemeToVisemeMap.set(ChinesePhonemeType.ONG, VisemeType.OU);

    // 特殊
    this.phonemeToVisemeMap.set(ChinesePhonemeType.SILENT, VisemeType.SILENT);
  }

  /**
   * 初始化口型转换矩阵
   */
  private initVisemeTransitionMatrix(): void {
    // 为每个口型创建转换概率
    for (const sourceViseme of Object.values(VisemeType)) {
      const transitionMap = new Map<VisemeType, number>();

      // 默认转换概率
      for (const targetViseme of Object.values(VisemeType)) {
        transitionMap.set(targetViseme as VisemeType, 0.1);
      }

      // 自身转换概率较高
      transitionMap.set(sourceViseme as VisemeType, 0.6);

      // 特定转换概率
      if (sourceViseme === VisemeType.MM) {
        transitionMap.set(VisemeType.AA, 0.3);
        transitionMap.set(VisemeType.OU, 0.2);
      }

      if (sourceViseme === VisemeType.AA) {
        transitionMap.set(VisemeType.MM, 0.2);
        transitionMap.set(VisemeType.OU, 0.2);
      }

      if (sourceViseme === VisemeType.OU) {
        transitionMap.set(VisemeType.MM, 0.2);
        transitionMap.set(VisemeType.AA, 0.2);
      }

      if (sourceViseme === VisemeType.EE) {
        transitionMap.set(VisemeType.DD, 0.2);
        transitionMap.set(VisemeType.CH, 0.2);
      }

      if (sourceViseme === VisemeType.SS) {
        transitionMap.set(VisemeType.EE, 0.3);
        transitionMap.set(VisemeType.DD, 0.2);
      }

      this.visemeTransitionMatrix.set(sourceViseme as VisemeType, transitionMap);
    }
  }

  /**
   * 初始化语音识别
   */
  private initSpeechRecognition(): void {
    try {
      // 检查浏览器支持
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.warn('浏览器不支持语音识别API');
        return;
      }

      // 创建语音识别实例
      this.speechRecognition = new SpeechRecognition();

      // 配置
      this.speechRecognition.continuous = true;
      this.speechRecognition.interimResults = true;
      this.speechRecognition.lang = this.config.speechRecognitionLang;

      // 结果处理
      this.speechRecognition.onresult = (event: any) => {
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];

          if (result.isFinal) {
            const text = result[0].transcript.trim();

            if (this.config.debug) {
              console.log('语音识别结果:', text);
            }

            // 分析音素
            if (this.config.usePhonemeAnalysis) {
              this.analyzePhonemes(text);
            }
          }
        }
      };

      // 错误处理
      this.speechRecognition.onerror = (event: any) => {
        console.error('语音识别错误:', event.error);
      };

      // 启动语音识别
      this.speechRecognition.start();

      if (this.config.debug) {
        console.log('语音识别已启动');
      }
    } catch (error) {
      console.error('初始化语音识别失败:', error);
    }
  }

  /**
   * 分析音素
   * @param text 文本
   */
  private analyzePhonemes(text: string): void {
    // 根据语言选择不同的音素分析方法
    if (this.config.speechRecognitionLang?.startsWith('zh')) {
      this.analyzeChinesePhonemes(text);
    } else {
      this.analyzeEnglishPhonemes(text);
    }
  }

  /**
   * 分析中文音素
   * @param text 中文文本
   */
  private analyzeChinesePhonemes(text: string): void {
    // 这里是简化的中文音素分析
    // 实际应用中应使用专业的中文音素分析库

    // 清空当前音素序列
    this.currentPhonemes = [];

    // 简单地将每个字符映射为一个音素
    for (const char of text) {
      // 根据字符选择音素
      // 这是一个非常简化的实现
      let phoneme: ChinesePhonemeType;

      // 简单的音素映射
      if (/[aeiouv]/.test(char.toLowerCase())) {
        // 元音
        switch (char.toLowerCase()) {
          case 'a': phoneme = ChinesePhonemeType.A; break;
          case 'e': phoneme = ChinesePhonemeType.E; break;
          case 'i': phoneme = ChinesePhonemeType.I; break;
          case 'o': phoneme = ChinesePhonemeType.O; break;
          case 'u': phoneme = ChinesePhonemeType.U; break;
          case 'v': phoneme = ChinesePhonemeType.V; break;
          default: phoneme = ChinesePhonemeType.A;
        }
      } else if (/[bcdfghjklmnpqrstwxyz]/.test(char.toLowerCase())) {
        // 辅音
        switch (char.toLowerCase()) {
          case 'b': phoneme = ChinesePhonemeType.B; break;
          case 'p': phoneme = ChinesePhonemeType.P; break;
          case 'm': phoneme = ChinesePhonemeType.M; break;
          case 'f': phoneme = ChinesePhonemeType.F; break;
          case 'd': phoneme = ChinesePhonemeType.D; break;
          case 't': phoneme = ChinesePhonemeType.T; break;
          case 'n': phoneme = ChinesePhonemeType.N; break;
          case 'l': phoneme = ChinesePhonemeType.L; break;
          case 'g': phoneme = ChinesePhonemeType.G; break;
          case 'k': phoneme = ChinesePhonemeType.K; break;
          case 'h': phoneme = ChinesePhonemeType.H; break;
          case 'j': phoneme = ChinesePhonemeType.J; break;
          case 'q': phoneme = ChinesePhonemeType.Q; break;
          case 'x': phoneme = ChinesePhonemeType.X; break;
          case 'z': phoneme = ChinesePhonemeType.Z; break;
          case 'c': phoneme = ChinesePhonemeType.C; break;
          case 's': phoneme = ChinesePhonemeType.S; break;
          case 'r': phoneme = ChinesePhonemeType.R; break;
          default: phoneme = ChinesePhonemeType.D;
        }
      } else {
        // 其他字符
        phoneme = ChinesePhonemeType.SILENT;
      }

      this.currentPhonemes.push(phoneme);
    }

    // 重置当前音素索引
    this.currentPhonemeIndex = 0;

    if (this.config.debug) {
      console.log('分析的中文音素序列:', this.currentPhonemes);
    }
  }

  /**
   * 分析英文音素
   * @param text 英文文本
   */
  private analyzeEnglishPhonemes(text: string): void {
    // 这里是简化的英文音素分析
    // 实际应用中应使用专业的英文音素分析库

    // 清空当前音素序列
    this.currentPhonemes = [];

    // 简单地将每个单词映射为音素序列
    const words = text.toLowerCase().split(/\s+/);

    for (const word of words) {
      // 跳过空单词
      if (!word) continue;

      // 为每个单词添加一个静音
      if (this.currentPhonemes.length > 0) {
        this.currentPhonemes.push(ChinesePhonemeType.SILENT);
      }

      // 简单地将每个字符映射为一个音素
      for (const char of word) {
        let phoneme: ChinesePhonemeType;

        // 简单的音素映射
        if (/[aeiou]/.test(char)) {
          // 元音
          switch (char) {
            case 'a': phoneme = ChinesePhonemeType.A; break;
            case 'e': phoneme = ChinesePhonemeType.E; break;
            case 'i': phoneme = ChinesePhonemeType.I; break;
            case 'o': phoneme = ChinesePhonemeType.O; break;
            case 'u': phoneme = ChinesePhonemeType.U; break;
            default: phoneme = ChinesePhonemeType.A;
          }
        } else if (/[bcdfghjklmnpqrstvwxyz]/.test(char)) {
          // 辅音
          switch (char) {
            case 'b': phoneme = ChinesePhonemeType.B; break;
            case 'p': phoneme = ChinesePhonemeType.P; break;
            case 'm': phoneme = ChinesePhonemeType.M; break;
            case 'f': phoneme = ChinesePhonemeType.F; break;
            case 'd': phoneme = ChinesePhonemeType.D; break;
            case 't': phoneme = ChinesePhonemeType.T; break;
            case 'n': phoneme = ChinesePhonemeType.N; break;
            case 'l': phoneme = ChinesePhonemeType.L; break;
            case 'g': phoneme = ChinesePhonemeType.G; break;
            case 'k': phoneme = ChinesePhonemeType.K; break;
            case 'h': phoneme = ChinesePhonemeType.H; break;
            case 'j': phoneme = ChinesePhonemeType.J; break;
            case 'q': phoneme = ChinesePhonemeType.K; break;
            case 'x': phoneme = ChinesePhonemeType.K; break;
            case 'z': phoneme = ChinesePhonemeType.Z; break;
            case 'c': phoneme = ChinesePhonemeType.S; break;
            case 's': phoneme = ChinesePhonemeType.S; break;
            case 'r': phoneme = ChinesePhonemeType.R; break;
            case 'v': phoneme = ChinesePhonemeType.F; break;
            case 'w': phoneme = ChinesePhonemeType.U; break;
            case 'y': phoneme = ChinesePhonemeType.I; break;
            default: phoneme = ChinesePhonemeType.D;
          }
        } else {
          // 其他字符
          phoneme = ChinesePhonemeType.SILENT;
        }

        this.currentPhonemes.push(phoneme);
      }
    }

    // 重置当前音素索引
    this.currentPhonemeIndex = 0;

    if (this.config.debug) {
      console.log('分析的英文音素序列:', this.currentPhonemes);
    }
  }

  /**
   * 更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    super.update(deltaTime);

    // 如果有音素序列，按照一定的速度播放
    if (this.currentPhonemes.length > 0 && this.currentPhonemeIndex < this.currentPhonemes.length) {
      // 获取当前音素
      const currentPhoneme = this.currentPhonemes[this.currentPhonemeIndex];

      // 获取对应的口型
      const viseme = this.phonemeToVisemeMap.get(currentPhoneme) || VisemeType.SILENT;

      // 设置口型
      this.setViseme(viseme);

      // 每隔一段时间前进到下一个音素
      // 这里简单地使用固定时间间隔，实际应用中可能需要更复杂的计时逻辑
      this.currentPhonemeIndex++;

      // 如果到达序列末尾，重置索引（循环播放）
      if (this.currentPhonemeIndex >= this.currentPhonemes.length) {
        this.currentPhonemeIndex = 0;
      }
    } else {
      // 如果没有音素序列，使用频谱分析
      this.processAudio();
    }
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   */
  private setViseme(viseme: VisemeType, weight: number = 1.0): void {
    // 保存上一个口型
    this.previousViseme = this.currentViseme;
    this.currentViseme = viseme;

    // 更新口型历史
    this.updateVisemeHistory(viseme);

    // 为所有实体设置口型
    for (const [entity, component] of this.components.entries()) {
      // 获取面部动画组件
      const facialAnimation = this.world.getComponent<FacialAnimationComponent>(entity, FacialAnimationComponent.TYPE);

      if (facialAnimation) {
        // 设置口型
        if (this.config.useSmoothTransition && this.previousViseme !== this.currentViseme) {
          // 使用平滑过渡
          const transitionTime = this.config.transitionTime || 0.1;
          facialAnimation.setViseme(viseme, weight, transitionTime);
        } else {
          // 直接设置
          facialAnimation.setViseme(viseme, weight);
        }
      }
    }
  }

  /**
   * 更新口型历史
   * @param viseme 口型
   */
  private updateVisemeHistory(viseme: VisemeType): void {
    // 添加到历史
    this.visemeHistory.push(viseme);

    // 限制历史大小
    if (this.visemeHistory.length > (this.config.contextWindowSize || 5)) {
      this.visemeHistory.shift();
    }
  }

  /**
   * 从上下文预测口型
   * @param currentViseme 当前口型
   * @returns 预测的口型
   */
  private predictVisemeFromContext(currentViseme: VisemeType): VisemeType {
    if (!this.config.useContextPrediction || this.visemeHistory.length === 0) {
      return currentViseme;
    }

    // 获取上一个口型
    const previousViseme = this.visemeHistory[this.visemeHistory.length - 1];

    // 获取转换概率
    const transitionMap = this.visemeTransitionMatrix.get(previousViseme);
    if (!transitionMap) {
      return currentViseme;
    }

    // 获取当前口型的转换概率
    const transitionProb = transitionMap.get(currentViseme) || 0.1;

    // 如果转换概率较低，可能是噪声，保持上一个口型
    if (transitionProb < 0.2) {
      return previousViseme;
    }

    return currentViseme;
  }
}
