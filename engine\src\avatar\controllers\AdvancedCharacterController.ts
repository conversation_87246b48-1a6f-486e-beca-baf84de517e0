/**
 * 高级角色控制器
 * 提供更丰富的角色控制功能，包括高级动作控制、环境感知和响应
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';
import { CharacterController, CharacterControllerOptions } from '../../physics/character/CharacterController';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { AnimationSystem } from '../../animation/AnimationSystem';
import { AnimationStateMachine } from '../../animation/AnimationStateMachine';
import { InputSystem } from '../../input/InputSystem';

import { Debug } from '../../utils/Debug';

/**
 * 角色移动模式
 */
export enum CharacterMovementMode {
  /** 行走模式 */
  WALKING = 'walking',
  /** 跑步模式 */
  RUNNING = 'running',
  /** 潜行模式 */
  CROUCHING = 'crouching',
  /** 爬行模式 */
  CRAWLING = 'crawling',
  /** 游泳模式 */
  SWIMMING = 'swimming',
  /** 攀爬模式 */
  CLIMBING = 'climbing',
  /** 飞行模式 */
  FLYING = 'flying'
}

/**
 * 角色状态
 */
export enum CharacterState {
  /** 空闲 */
  IDLE = 'idle',
  /** 行走 */
  WALK = 'walk',
  /** 跑步 */
  RUN = 'run',
  /** 跳跃 */
  JUMP = 'jump',
  /** 下落 */
  FALL = 'fall',
  /** 着陆 */
  LAND = 'land',
  /** 潜行 */
  CROUCH = 'crouch',
  /** 爬行 */
  CRAWL = 'crawl',
  /** 游泳 */
  SWIM = 'swim',
  /** 攀爬 */
  CLIMB = 'climb',
  /** 飞行 */
  FLY = 'fly',
  /** 受伤 */
  HURT = 'hurt',
  /** 死亡 */
  DEATH = 'death',
  /** 交互 */
  INTERACT = 'interact',
  /** 攻击 */
  ATTACK = 'attack',
  /** 防御 */
  DEFEND = 'defend',
  /** 使用物品 */
  USE_ITEM = 'use_item'
}

/**
 * 高级角色控制器配置
 */
export interface AdvancedCharacterControllerConfig {
  /** 行走速度 */
  walkSpeed?: number;
  /** 跑步速度 */
  runSpeed?: number;
  /** 潜行速度 */
  crouchSpeed?: number;
  /** 爬行速度 */
  crawlSpeed?: number;
  /** 游泳速度 */
  swimSpeed?: number;
  /** 攀爬速度 */
  climbSpeed?: number;
  /** 飞行速度 */
  flySpeed?: number;
  /** 跳跃力量 */
  jumpForce?: number;
  /** 重力 */
  gravity?: number;
  /** 转向速度 */
  turnSpeed?: number;
  /** 空中控制系数 */
  airControl?: number;
  /** 是否使用物理系统 */
  usePhysics?: boolean;
  /** 是否使用动画状态机 */
  useStateMachine?: boolean;
  /** 是否使用混合空间 */
  useBlendSpace?: boolean;
  /** 是否使用IK */
  useIK?: boolean;
  /** 是否使用环境感知 */
  useEnvironmentAwareness?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
  /** 物理控制器选项 */
  physicsControllerOptions?: CharacterControllerOptions;
}

/**
 * 高级角色控制器
 */
export class AdvancedCharacterController {
  /** 关联的实体 */
  private entity: Entity | null = null;

  /** 世界 */
  private world: World | null = null;

  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null = null;

  /** 动画系统 */
  private animationSystem: AnimationSystem | null = null;

  /** 输入系统 */
  private inputSystem: InputSystem | null = null;

  /** 物理控制器 */
  private physicsController: CharacterController | null = null;

  /** 动画状态机 */
  private stateMachine: AnimationStateMachine | null = null;

  /** 配置 */
  private config: AdvancedCharacterControllerConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AdvancedCharacterControllerConfig = {
    walkSpeed: 2.0,
    runSpeed: 5.0,
    crouchSpeed: 1.0,
    crawlSpeed: 0.5,
    swimSpeed: 1.5,
    climbSpeed: 1.0,
    flySpeed: 8.0,
    jumpForce: 5.0,
    gravity: 9.8,
    turnSpeed: 2.0,
    airControl: 0.3,
    usePhysics: true,
    useStateMachine: true,
    useBlendSpace: true,
    useIK: true,
    useEnvironmentAwareness: true,
    debug: false
  };

  /** 当前移动模式 */
  private movementMode: CharacterMovementMode = CharacterMovementMode.WALKING;

  /** 当前状态 */
  private currentState: CharacterState = CharacterState.IDLE;

  /** 移动方向 */
  private moveDirection: THREE.Vector3 = new THREE.Vector3();

  /** 速度 */
  private velocity: THREE.Vector3 = new THREE.Vector3();

  /** 是否在地面上 */
  private isGrounded: boolean = true;

  /** 是否正在跳跃 */
  private isJumping: boolean = false;

  /** 是否正在跑步 */
  private isRunning: boolean = false;

  /** 是否正在潜行 */
  private isCrouching: boolean = false;

  /** 是否正在爬行 */
  private isCrawling: boolean = false;

  /** 是否正在游泳 */
  private isSwimming: boolean = false;

  /** 是否正在攀爬 */
  private isClimbing: boolean = false;

  /** 是否正在飞行 */
  private isFlying: boolean = false;

  /** 是否正在交互 */
  private isInteracting: boolean = false;

  /** 是否正在攻击 */
  private isAttacking: boolean = false;

  /** 是否正在防御 */
  private isDefending: boolean = false;

  /** 是否正在使用物品 */
  private isUsingItem: boolean = false;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<AdvancedCharacterControllerConfig> = {}) {
    this.config = { ...AdvancedCharacterController.DEFAULT_CONFIG, ...config };
  }

  /**
   * 初始化控制器
   * @param entity 实体
   * @param world 世界
   */
  public initialize(entity: Entity, world: World): void {
    if (this.initialized) return;

    this.entity = entity;
    this.world = world;

    // 获取系统
    this.physicsSystem = world.getSystem<PhysicsSystem>('PhysicsSystem');
    this.animationSystem = world.getSystem<AnimationSystem>('AnimationSystem');
    this.inputSystem = world.getSystem<InputSystem>('InputSystem');

    // 初始化物理控制器
    if (this.config.usePhysics && this.physicsSystem) {
      this.initPhysicsController();
    }

    // 初始化动画状态机
    if (this.config.useStateMachine && this.animationSystem) {
      this.initAnimationStateMachine();
    }

    // 初始化输入处理
    if (this.inputSystem) {
      this.initInputHandling();
    }

    this.initialized = true;

    if (this.config.debug) {
      Debug.log('高级角色控制器初始化完成', this);
    }
  }

  /**
   * 更新控制器
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.entity) return;

    // 更新物理
    this.updatePhysics(deltaTime);

    // 更新动画
    this.updateAnimation(deltaTime);

    // 更新环境感知
    if (this.config.useEnvironmentAwareness) {
      this.updateEnvironmentAwareness(deltaTime);
    }

    // 发出更新事件
    this.eventEmitter.emit('update', { deltaTime });
  }

  /**
   * 初始化物理控制器
   */
  private initPhysicsController(): void {
    // 实现物理控制器初始化
  }

  /**
   * 初始化动画状态机
   */
  private initAnimationStateMachine(): void {
    // 实现动画状态机初始化
  }

  /**
   * 初始化输入处理
   */
  private initInputHandling(): void {
    // 实现输入处理初始化
  }

  /**
   * 更新物理
   * @param deltaTime 时间增量（秒）
   */
  private updatePhysics(deltaTime: number): void {
    // 实现物理更新
    // deltaTime 用于物理计算和状态更新
    if (this.physicsController && this.entity) {
      // 这里可以添加物理更新逻辑
    }
  }

  /**
   * 更新动画
   * @param deltaTime 时间增量（秒）
   */
  private updateAnimation(deltaTime: number): void {
    // 实现动画更新
    // deltaTime 用于动画状态机更新
    if (this.stateMachine && this.animationSystem && deltaTime > 0) {
      // 这里可以添加动画更新逻辑
      // 例如：this.stateMachine.update(deltaTime);
    }
  }

  /**
   * 更新环境感知
   * @param deltaTime 时间增量（秒）
   */
  private updateEnvironmentAwareness(deltaTime: number): void {
    // 实现环境感知更新
    // deltaTime 用于时间相关的环境感知逻辑
    if (deltaTime > 0 && this.config.useEnvironmentAwareness) {
      // 这里可以添加环境感知更新逻辑
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: EventCallback): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 获取当前移动模式
   */
  public getMovementMode(): CharacterMovementMode {
    return this.movementMode;
  }

  /**
   * 设置移动模式
   * @param mode 移动模式
   */
  public setMovementMode(mode: CharacterMovementMode): void {
    this.movementMode = mode;
  }

  /**
   * 获取当前状态
   */
  public getCurrentState(): CharacterState {
    return this.currentState;
  }

  /**
   * 设置当前状态
   * @param state 状态
   */
  public setCurrentState(state: CharacterState): void {
    this.currentState = state;
  }

  /**
   * 获取移动方向
   */
  public getMoveDirection(): THREE.Vector3 {
    return this.moveDirection.clone();
  }

  /**
   * 设置移动方向
   * @param direction 移动方向
   */
  public setMoveDirection(direction: THREE.Vector3): void {
    this.moveDirection.copy(direction);
  }

  /**
   * 获取速度
   */
  public getVelocity(): THREE.Vector3 {
    return this.velocity.clone();
  }

  /**
   * 设置速度
   * @param velocity 速度
   */
  public setVelocity(velocity: THREE.Vector3): void {
    this.velocity.copy(velocity);
  }

  /**
   * 是否在地面上
   */
  public getIsGrounded(): boolean {
    return this.isGrounded;
  }

  /**
   * 设置是否在地面上
   * @param grounded 是否在地面上
   */
  public setIsGrounded(grounded: boolean): void {
    this.isGrounded = grounded;
  }
}
