/**
 * 系统基类
 * 负责处理特定类型的组件和功能
 */
import type { Engine } from './Engine';
import type { World } from './World';
import { EventEmitter } from '../utils/EventEmitter';

export abstract class System extends EventEmitter {
  /** 系统类型 */
  private type: string;

  /** 引擎引用 */
  protected engine: Engine | null = null;

  /** 世界引用 */
  protected world: World | null = null;

  /** 优先级（数字越小优先级越高） */
  private priority: number;

  /** 是否启用 */
  private enabled: boolean = true;

  /**
   * 创建系统实例
   * @param priority 优先级（数字越小优先级越高）
   */
  constructor(priority: number = 0) {
    super();
    this.type = this.constructor.name;
    this.priority = priority;
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return this.type;
  }

  /**
   * 设置引擎引用
   * @param engine 引擎实例
   */
  public setEngine(engine: Engine): void {
    this.engine = engine;
  }

  /**
   * 获取引擎引用
   * @returns 引擎实例
   */
  public getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 设置世界引用
   * @param world 世界实例
   */
  public setWorld(world: World): void {
    this.world = world;
    this.engine = world.getEngine();
  }

  /**
   * 获取世界引用
   * @returns 世界实例
   */
  public getWorld(): World | null {
    return this.world;
  }

  /**
   * 获取优先级
   * @returns 优先级
   */
  public getPriority(): number {
    return this.priority;
  }

  /**
   * 设置优先级
   * @param priority 优先级
   */
  public setPriority(priority: number): void {
    this.priority = priority;
  }

  /**
   * 设置启用状态
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    if (this.enabled === enabled) {
      return;
    }

    this.enabled = enabled;

    if (enabled) {
      this.onEnable();
    } else {
      this.onDisable();
    }

    // 发出启用状态变更事件
    this.emit('enabledChanged', enabled);
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 子类可以重写此方法
  }

  /**
   * 当系统启用时调用
   */
  protected onEnable(): void {
    // 子类可以重写此方法
  }

  /**
   * 当系统禁用时调用
   */
  protected onDisable(): void {
    // 子类可以重写此方法
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 子类可以重写此方法
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    // 子类可以重写此方法
  }

  /**
   * 后更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public lateUpdate(deltaTime: number): void {
    // 子类可以重写此方法
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除引擎引用
    this.engine = null;

    // 清除世界引用
    this.world = null;

    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
